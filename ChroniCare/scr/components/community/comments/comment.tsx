import { View, Text, StyleSheet } from 'react-native';
import React, { useMemo } from 'react';
import { useTheme } from '@/scr/context/themeContext';
import { MessagesSquare } from 'lucide-react-native';

interface CommentCounterProps {
  count: number;
}

const CommentCounter = ({ count }: CommentCounterProps) => {
  const { theme } = useTheme();

  const styles = useMemo(() => StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: theme.colors.Background.background0,
        alignSelf: 'flex-start',
        gap: theme.spacing.spacing.s0_5,

    },
    iconWrapper: {
        width: 25,
        height: 25,
        borderRadius: 14,
        borderWidth: 1,
        borderColor: theme.colors.Background.background0,
        backgroundColor: theme.colors.Background.background100,
        justifyContent: 'center',
        alignItems: 'center',
    },
    countText: {
        ...theme.textVariants.text('sm', 'regular'),
        color: theme.colors.Text.text900,
    }
  }), [theme]);


  return (
    <View style={styles.container}>
        <View style={styles.iconWrapper}>
            <MessagesSquare size={16} color={theme.colors.Text.text900} strokeWidth={2.5} />
        </View>
        <Text style={styles.countText}>{count}</Text>
    </View>
  );
};

export default CommentCounter;
