import React, { useC<PERSON>back, useMemo, forwardRef, useImperativeHandle } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import {
  BottomSheetModal,
  BottomSheetView,
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
} from '@gorhom/bottom-sheet';
import { useTheme } from '@/scr/context/themeContext';
import { ReactionCount } from '@/scr/graphql/fragments';

interface ReactionCountsBottomSheetProps {
  reactionCounts: ReactionCount | null;
}

export interface ReactionCountsBottomSheetRef {
  present: () => void;
  dismiss: () => void;
}

const ReactionCountsBottomSheet = forwardRef<ReactionCountsBottomSheetRef, ReactionCountsBottomSheetProps>(
  ({ reactionCounts }, ref) => {
    const { theme } = useTheme();
    const bottomSheetModalRef = React.useRef<BottomSheetModal>(null);

    // Snap points for the bottom sheet
    const snapPoints = useMemo(() => ['50%'], []);

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      present: () => {
        bottomSheetModalRef.current?.present();
      },
      dismiss: () => {
        bottomSheetModalRef.current?.dismiss();
      },
    }));

    // Backdrop component
    const renderBackdrop = useCallback(
      (props: BottomSheetBackdropProps) => (
        <BottomSheetBackdrop
          {...props}
          disappearsOnIndex={-1}
          appearsOnIndex={0}
          enableTouchThrough={false}
        />
      ),
      []
    );

    const styles = useMemo(
      () =>
        StyleSheet.create({
          container: {
            flex: 1,
            padding: theme.spacing.spacing.s4,
          },
          header: {
            alignItems: 'center',
            marginBottom: theme.spacing.spacing.s4,
          },
          title: {
            ...theme.textVariants.text('lg', 'semibold'),
            color: theme.colors.Text.text900,
            marginBottom: theme.spacing.spacing.s2,
          },
          subtitle: {
            ...theme.textVariants.text('sm', 'regular'),
            color: theme.colors.Text.text600,
            textAlign: 'center',
          },
          content: {
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
          },
          placeholderText: {
            ...theme.textVariants.text('md', 'regular'),
            color: theme.colors.Text.text500,
            textAlign: 'center',
          },
        }),
      [theme]
    );

    // Calculate total reactions
    const totalReactions = useMemo(() => {
      if (!reactionCounts) return 0;
      return (
        (reactionCounts.love || 0) +
        (reactionCounts.withYou || 0) +
        (reactionCounts.funny || 0) +
        (reactionCounts.insightful || 0) +
        (reactionCounts.poop || 0)
      );
    }, [reactionCounts]);

    return (
      <BottomSheetModal
        ref={bottomSheetModalRef}
        index={0}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        enablePanDownToClose={true}
        backgroundStyle={{
          backgroundColor: theme.colors.Background.background0,
        }}
        handleIndicatorStyle={{
          backgroundColor: theme.colors.Background.background900,
        }}
      >
        <BottomSheetView style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>Reactions</Text>
            <Text style={styles.subtitle}>
              {totalReactions} {totalReactions === 1 ? 'person' : 'people'} reacted to this
            </Text>
          </View>
          
          <View style={styles.content}>
            <Text style={styles.placeholderText}>
              Reaction details will be implemented here
            </Text>
          </View>
        </BottomSheetView>
      </BottomSheetModal>
    );
  }
);

ReactionCountsBottomSheet.displayName = 'ReactionCountsBottomSheet';

export default ReactionCountsBottomSheet;
