import { View, Text, StyleSheet } from 'react-native';
import React, { useMemo } from 'react';
import { useTheme } from '@/scr/context/themeContext';
import { ReactionCount } from '@/scr/graphql/fragments';
import { useReactionDisplay } from '@/scr/hooks/useReactionDisplay';
import ReactionIcon from './community/reactions/ReactionIcon';

interface ReactionCountsProps {
  reactionCounts: ReactionCount | null;
}

const ReactionCounts = ({ reactionCounts }: ReactionCountsProps) => {
  const { theme } = useTheme();
  
  // Use the custom hook for reaction display logic
  const { totalReactions, displayedReactions } = useReactionDisplay(reactionCounts);

  const styles = useMemo(
    () =>
      StyleSheet.create({
        container: {
          flexDirection: 'row',
          alignItems: 'center',
          borderRadius: 20,
          borderColor: theme.colors.Text.text0,
          alignSelf: 'flex-start',
          gap: theme.spacing.spacing.s0_5,
        },
        iconsContainer: {
          flexDirection: 'row',
          alignItems: 'center',
        },
        overlappingIcon: {
          marginLeft: -9,
        },
        totalText: {
          ...theme.textVariants.text('sm', 'regular'),
          color: theme.colors.Text.text900,
        },
      }),
    [theme],
  );

  // Don't render if there are no reactions
  if (!reactionCounts || totalReactions === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.iconsContainer}>
        {displayedReactions.map(({ key }, index) => (
          <View
            key={key}
            style={[
              index > 0 && styles.overlappingIcon,
              { zIndex: displayedReactions.length - index },
            ]}>
            <ReactionIcon 
              reactionType={key}
              isSelected={false}
              showBorder={true}
              size={16}
              showBackground={true}
            />
          </View>
        ))}
      </View>
      <View>
        <Text style={styles.totalText}>{totalReactions}</Text>
      </View>
    </View>
  );
};

export default React.memo(ReactionCounts);