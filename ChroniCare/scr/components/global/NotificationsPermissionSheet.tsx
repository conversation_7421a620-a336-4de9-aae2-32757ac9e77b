import React, { useCallback, useMemo } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop, BottomSheetBackdropProps } from '@gorhom/bottom-sheet';
import { useTheme } from '../../context/themeContext';
import { Bell } from 'lucide-react-native';
import { Button } from '../button';

interface NotificationsPermissionSheetProps {
  isVisible: boolean;
  onActivate: () => void;
  onDismiss: () => void;
}

export const NotificationsPermissionSheet: React.FC<NotificationsPermissionSheetProps> = ({
  isVisible,
  onActivate,
  onDismiss,
}) => {
  const theme = useTheme();
  const bottomSheetRef = React.useRef<BottomSheet>(null);

  // Snap points for the bottom sheet
  const snapPoints = useMemo(() => ['80%'], []);

  // Handle sheet changes
  const handleSheetChanges = useCallback((index: number) => {
    if (index === -1) {
      // Sheet was closed
      onDismiss();
    }
  }, [onDismiss]);

  // Open/close sheet based on isVisible prop
  React.useEffect(() => {
    if (isVisible) {
      bottomSheetRef.current?.expand();
    } else {
      bottomSheetRef.current?.close();
    }
  }, [isVisible]);

  const handleActivate = () => {
    onActivate();
    bottomSheetRef.current?.close();
  };

  const handleNotNow = () => {
    onDismiss();
    bottomSheetRef.current?.close();
  };

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop {...props} enableTouchThrough={true} />
    ),
    []
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 24,
      backgroundColor: theme.theme.colors.Background.background0,
    },
    contentContainer: {
      gap: theme.theme.spacing.spacing.s4,
    },
    header: {
      alignItems: 'center',
      flexDirection: 'row',
      gap: theme.theme.spacing.spacing.s1,
    },
    title: {
      fontSize: theme.theme.typography.headingSize.lg,
      fontWeight: 'bold',
      color: theme.theme.colors.Text.text900,
    },
    content: {
      gap: theme.theme.spacing.spacing.s1,
    },
    divider: {
      height: 1,
      backgroundColor: theme.theme.colors.Background.background200,
      marginVertical: theme.theme.spacing.spacing.s3,
    },
    description: {
      fontSize: theme.theme.typography.textSize.sm,
      color: theme.theme.colors.Text.text900,
      textAlign: 'left',
    },
    buttonContainer: {
      gap: theme.theme.spacing.spacing.s3,
    },

    activateButton: {
      backgroundColor: theme.theme.colors.Primary.primary500,
      paddingVertical: 16,
      paddingHorizontal: 24,
      borderRadius: 12,
      alignItems: 'center',
    },
    activateButtonText: {
      color: theme.theme.colors.Text.text0,
      fontSize: theme.theme.typography.textSize.md,
      fontWeight: '600',
    },
    dismissButton: {
      backgroundColor: 'transparent',
      paddingVertical: 16,
      paddingHorizontal: 24,
      borderRadius: 12,
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.theme.colors.Background.background0,
    },
    dismissButtonText: {
      color: theme.theme.colors.Text.text0,
      fontSize: theme.theme.typography.textSize.md,
      fontWeight: '500',
    },
    closeButton: {
      position: 'absolute',
      top: theme.theme.spacing.spacing.s4,
      right: theme.theme.spacing.spacing.s4,
      width: theme.theme.typography.textSize.xl,
      height: theme.theme.typography.textSize.xl,
      borderRadius: 16,
      backgroundColor: theme.theme.colors.Background.background0,
      alignItems: 'center',
      justifyContent: 'center',
    },
  });

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={-1}
      backdropComponent={renderBackdrop}
      snapPoints={snapPoints}
      onChange={handleSheetChanges}
      enablePanDownToClose={true}
      backgroundStyle={{
        backgroundColor: theme.theme.colors.Background.background0,
      }}
      handleIndicatorStyle={{
        backgroundColor: theme.theme.colors.Background.background900,
      }}
    >
      <BottomSheetView style={styles.container}>
        <View style={styles.contentContainer}>
        <View style={styles.header}>
          <Bell size={24} color={theme.theme.colors.Primary.primary500} />
          <Text style={styles.title}>Stay Connected</Text>
        </View>

        <View style={styles.content}>
          <Text style={styles.description}>
            We'll send you notifications about new replies to your posts, important community updates, and health reminders. You can change these settings anytime.
          </Text>
        </View>
        <View style={styles.divider} />
        </View>

        <View style={styles.buttonContainer}>
          <Button onPress={handleActivate} variant="neutral" size="small" backgroundColor={theme.theme.colors.Primary.primary500} title="Activate Notifications" />

          <Button onPress={handleNotNow} variant="outline" size="small" title="Not Now" />

        </View>
      </BottomSheetView>
    </BottomSheet>
  );
};
