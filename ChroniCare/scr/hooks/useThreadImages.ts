import { useState } from 'react';
import { useMutation } from '@apollo/client';
import { CREATE_THREAD_IMAGE_UPLOAD_URL, DELETE_THREAD_IMAGE } from '../graphql/queries';
import { Alert } from 'react-native';
import * as ImagePicker from 'expo-image-picker';

export interface UseThreadImagesResult {
  uploadedImages: string[];
  isUploading: boolean;
  isDeleting: boolean;
  error: any;
  uploadImage: (imageUri: string, mimeType: string) => Promise<string | null>;
  selectAndUploadImage: () => Promise<void>;
  removeImage: (imageUrl: string) => Promise<void>;
  clearImages: () => void;
}

export const useThreadImages = (): UseThreadImagesResult => {
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<any>(null);

  const [createUploadUrl] = useMutation(CREATE_THREAD_IMAGE_UPLOAD_URL, {
    onError: (error) => {
      console.error('Error creating upload URL:', error);
      setError(error);
    }
  });

  const [deleteThreadImage] = useMutation(DELETE_THREAD_IMAGE, {
    onError: (error) => {
      console.error('Error deleting image:', error);
      setError(error);
    }
  });

  const uploadImage = async (imageUri: string, mimeType: string): Promise<string | null> => {
    try {
      setIsUploading(true);
      setError(null);

      console.log('🖼️ ===== FRONTEND IMAGE UPLOAD STARTED =====');
      console.log(`📱 Image URI: ${imageUri}`);
      console.log(`📄 MIME Type: ${mimeType}`);

      // Step 1: Get signed upload URL from backend
      console.log('⏳ Step 1: Requesting signed URL from backend...');
      const { data: uploadData } = await createUploadUrl({
        variables: { contentType: mimeType },
      });
      const { signedUrl, publicUrl } = uploadData.createThreadImageUploadUrl;

      console.log('✅ Step 1: Received signed URL from backend');
      console.log(`🔗 Signed URL length: ${signedUrl.length} characters`);
      console.log(`🔗 Signed URL preview: ${signedUrl.substring(0, 200)}...`);
      console.log(`🌐 Public URL: ${publicUrl}`);

      // Analyze the signed URL
      try {
        const urlObj = new URL(signedUrl);
        console.log('🔍 Signed URL Analysis:');
        console.log(`Host: ${urlObj.hostname}`);
        console.log(`Path: ${urlObj.pathname}`);
        console.log(`Search params count: ${urlObj.searchParams.size}`);
        
        // Log key parameters
        const importantParams = ['X-Goog-Algorithm', 'X-Goog-Credential', 'X-Goog-Date', 'X-Goog-Expires', 'X-Goog-Signature'];
        importantParams.forEach(param => {
          const value = urlObj.searchParams.get(param);
          if (value) {
            console.log(`${param}: ${value.substring(0, 50)}${value.length > 50 ? '...' : ''}`);
          }
        });
      } catch (urlError) {
        console.warn('⚠️ Could not parse signed URL:', urlError instanceof Error ? urlError.message : String(urlError));
      }

      // Step 2: Prepare the image blob
      console.log('⏳ Step 2: Preparing image blob...');
      const response = await fetch(imageUri);
      if (!response.ok) {
        throw new Error(`Failed to fetch image from URI: ${response.status} ${response.statusText}`);
      }
      
      const blob = await response.blob();
      
      console.log('✅ Step 2: Image blob prepared');
      console.log('📊 Blob details:', { 
        type: blob.type, 
        size: blob.size,
        sizeKB: (blob.size / 1024).toFixed(2),
        sizeMB: (blob.size / (1024 * 1024)).toFixed(2)
      });
      console.log(`🔍 Blob type matches MIME type: ${blob.type === mimeType}`);

      // Step 3: Upload image directly to Google Cloud Storage
      console.log('⏳ Step 3: Uploading to Google Cloud Storage...');
      console.log(`🎯 Upload endpoint: ${new URL(signedUrl).origin}${new URL(signedUrl).pathname}`);
      console.log(`📦 Upload headers: Content-Type: ${mimeType}`);

      const uploadResponse = await fetch(signedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': mimeType,
        },
        body: blob,
      });

      console.log('📡 Upload response received');
      console.log(`📊 Upload response status: ${uploadResponse.status} ${uploadResponse.statusText}`);
      console.log('📋 Upload response headers:');
      
      // Log all response headers
      const responseHeaders: Record<string, string> = {};
      uploadResponse.headers.forEach((value, key) => {
        responseHeaders[key] = value;
        console.log(`  ${key}: ${value}`);
      });

      if (!uploadResponse.ok) {
        const responseText = await uploadResponse.text();
        console.error('❌ Upload failed!');
        console.error(`Status: ${uploadResponse.status} ${uploadResponse.statusText}`);
        console.error('Response body:', responseText);
        
        // Try to parse error details if it's XML
        if (responseText.includes('<Error>')) {
          console.error('🔍 Parsed error details:');
          const codeMatch = responseText.match(/<Code>(.*?)<\/Code>/);
          const messageMatch = responseText.match(/<Message>(.*?)<\/Message>/);
          const detailsMatch = responseText.match(/<Details>(.*?)<\/Details>/);
          
          if (codeMatch) console.error(`Error Code: ${codeMatch[1]}`);
          if (messageMatch) console.error(`Error Message: ${messageMatch[1]}`);
          if (detailsMatch) console.error(`Error Details: ${detailsMatch[1]}`);
        }
        
        throw new Error(`Failed to upload image to storage: ${uploadResponse.status} - ${responseText}`);
      }

      console.log('✅ Step 3: Upload to Google Cloud Storage successful!');

      // Step 4: Add to uploaded images list
      console.log('⏳ Step 4: Adding to uploaded images list...');
      setUploadedImages(prev => [...prev, publicUrl]);
      console.log('✅ Step 4: Added to uploaded images list');
      console.log('🎉 ===== FRONTEND IMAGE UPLOAD COMPLETED SUCCESSFULLY =====');
      console.log(`📸 Final public URL: ${publicUrl}`);
      
      return publicUrl;
    } catch (error) {
      console.error('❌ ===== FRONTEND IMAGE UPLOAD FAILED =====');
      console.error('Error type:', error instanceof Error ? error.constructor.name : typeof error);
      console.error('Error message:', error instanceof Error ? error.message : String(error));
      console.error('Full error object:', error);
      
      if (error instanceof Error && error.stack) {
        console.error('Error stack:', error.stack);
      }
      
      setError(error);
      Alert.alert('Upload Error', 'Failed to upload image. We are working on a fix!');
      return null;
    } finally {
      setIsUploading(false);
      console.log('🏁 Upload process completed (success or failure)');
    }
  };

  const selectAndUploadImage = async (): Promise<void> => {
    try {
      // Request permissions
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (permissionResult.granted === false) {
        Alert.alert('Permission Required', 'Permission to access media library is required!');
        return;
      }

      // Pick image
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: false,
        quality: 0.8,
        base64: false,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const mimeType = result.assets[0].mimeType;
        const imageUri = result.assets[0].uri;
        if (!mimeType || !imageUri) {
          throw new Error('Invalid image data');
        }
        await uploadImage(imageUri, mimeType);
      }
    } catch (error) {
      console.error('Error selecting image:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  const removeImage = async (imageUrl: string): Promise<void> => {
    try {
      setIsDeleting(true);
      setError(null);

      console.log('🗑️ ===== DELETING IMAGE FROM STORAGE =====');
      console.log(`🌐 Image URL: ${imageUrl}`);

      // Remove from UI immediately for better UX
      setUploadedImages(prev => prev.filter(url => url !== imageUrl));

      // Delete from cloud storage
      console.log('⏳ Calling backend to delete image...');
      await deleteThreadImage({
        variables: { imageUrl }
      });

      console.log('✅ Successfully deleted image from storage');
    } catch (error) {
      console.error('❌ ===== IMAGE DELETION FAILED =====');
      console.error('Error:', error);

      // Re-add the image to the list since deletion failed
      setUploadedImages(prev => [...prev, imageUrl]);

      setError(error);
      Alert.alert(
        'Delete Error',
        'Failed to delete image from storage. The image has been restored to your post.'
      );
    } finally {
      setIsDeleting(false);
    }
  };

  const clearImages = () => {
    setUploadedImages([]);
    setError(null);
  };

  return {
    uploadedImages,
    isUploading,
    isDeleting,
    error,
    uploadImage,
    selectAndUploadImage,
    removeImage,
    clearImages,
  };
};