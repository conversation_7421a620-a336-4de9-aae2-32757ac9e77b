import { useMutation } from '@apollo/client';
import { DELETE_THREAD_IMAGE } from '../graphql/queries';
import { useDraft, PostDraft } from './useDraft';

export const useDraftWithImages = () => {
  const { draft, isLoaded, saveDraft: originalSaveDraft, clearDraft: originalClearDraft, setDraft } = useDraft();
  
  const [deleteThreadImage] = useMutation(DELETE_THREAD_IMAGE, {
    onError: (error) => {
      console.error('Error deleting draft image:', error);
    }
  });

  const saveDraft = async (title: string, content: string, imageUrls?: string[]) => {
    await originalSaveDraft(title, content, imageUrls);
  };

  const clearDraft = async () => {
    // Clean up any images associated with the draft
    if (draft?.imageUrls && draft.imageUrls.length > 0) {
      console.log('🧹 Cleaning up draft images...');
      
      // Delete images from storage (fire and forget - don't block the UI)
      draft.imageUrls.forEach(async (imageUrl) => {
        try {
          await deleteThreadImage({
            variables: { imageUrl }
          });
          console.log(`✅ Deleted draft image: ${imageUrl}`);
        } catch (error) {
          console.warn(`⚠️ Failed to delete draft image: ${imageUrl}`, error);
          // Don't throw - we don't want to block draft clearing for image cleanup failures
        }
      });
    }
    
    await originalClearDraft();
  };

  return {
    draft,
    isLoaded,
    saveDraft,
    clearDraft,
    setDraft
  };
};
