import 'dotenv/config'; 

const isDevelopment = process.env.APP_VARIANT === 'development';

console.log('🔔 Environment: ', process.env.APP_VARIANT);

export default {
  expo: {
    name: 'ChroniCare',
    slug: 'chronicare',
    version: '1.2.0',
    orientation: 'portrait',
    icon: './assets/images/appIcon.png',
    scheme: 'chronicare',
    userInterfaceStyle: 'automatic',
    newArchEnabled: true,
    ios: {
      supportsTablet: true,
      bundleIdentifier: 'io.chronicare.ChroniCare',
      googleServicesFile: './firebase/GoogleService-Info.plist',
      infoPlist: {
        NSPhotoLibraryUsageDescription: 'This app needs access to your photo library to let you select a profile picture.',
        NSCameraUsageDescription: 'This app needs access to your camera to let you take a profile picture.',
        NSMicrophoneUsageDescription: 'This app needs access to your microphone for voice features.',
        CFBundleDisplayName: 'ChroniCare',
        ITSAppUsesNonExemptEncryption: false,
      },
    },
    android: {
      adaptiveIcon: {
        foregroundImage: './assets/images/appIcon.png',
        backgroundColor: '#ffffff',
      },
      googleServicesFile: './firebase/google-services.json',
      edgeToEdgeEnabled: true,
      package: 'io.chronicare.ChroniCare',
      permissions: [
        'android.permission.RECORD_AUDIO',
        'android.permission.CAMERA',
        'android.permission.READ_EXTERNAL_STORAGE',
        'android.permission.WRITE_EXTERNAL_STORAGE',
      ],
    },
    web: {
      bundler: 'metro',
      output: 'static',
      favicon: './assets/images/appIcon.png',
    },
    plugins: [
      'expo-router',
      [
        'expo-splash-screen',
        {
          image: './assets/images/logoTextBlackBig.png',
          resizeMode: 'contain',
          backgroundColor: '#ffffff',
          width: '95%',
        },
      ],
      'expo-font',
      'expo-web-browser',
      '@react-native-firebase/app',
      'expo-notifications',
      '@react-native-firebase/auth',
      [
        '@react-native-google-signin/google-signin',
        {
          iosUrlScheme:
            'com.googleusercontent.apps.1056297800349-2pp8atqq8i7svalp0n0pslcqj0uve3p4',
        },
      ],
      [
        'expo-build-properties',
        {
          ios: {
            useFrameworks: 'static',
          },
        },
      ],
      'expo-localization',
      [
        'expo-image-picker',
        {
          photosPermission: 'The app needs access to your photos to let you select a profile picture.',
        },
      ],
    ],
    experiments: {
      typedRoutes: true,
    },
    extra: {
      router: {},
      eas: {
        projectId: '1f5aff68-976a-455d-8121-1adc720b18a3',
      },
    },
    owner: 'alexdadi',
    updates: {
      enabled: true,
      url: 'https://u.expo.dev/1f5aff68-976a-455d-8121-1adc720b18a3',
      checkAutomatically: 'ON_LOAD',
      runtimeVersion: {
        policy: 'appVersion',
      },
    },
    runtimeVersion: '1.2.0',
  },
};