// src/storage/storage.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Storage } from '@google-cloud/storage';
import * as path from 'path';

@Injectable()
export class StorageService {
  private readonly storage: Storage;
  private readonly bucketName: string;
  private readonly logger = new Logger(StorageService.name);

  constructor(private readonly configService: ConfigService) {
    const nodeEnv = this.configService.get<string>('NODE_ENV');
    const serviceAccountJson = this.configService.get<string>('GCP_SERVICE_ACCOUNT_JSON');
    const projectIdFromEnv = this.configService.get<string>('GOOGLE_CLOUD_PROJECT_ID');
    this.bucketName = this.configService.get<string>('GOOGLE_CLOUD_STORAGE_BUCKET') || 'chronicare-uploads-prod';

    let storageConfig: ConstructorParameters<typeof Storage>[0] = {
      projectId: projectIdFromEnv,
    };

    if (nodeEnv === 'development' && serviceAccountJson) {
      this.logger.log('Production environment detected. Initializing with Service Account credentials.');
      try {
        const credentials = JSON.parse(serviceAccountJson);
        storageConfig = {
          projectId: credentials.project_id,
          credentials,
        };
        this.logger.log(`StorageService initialized for project: ${credentials.project_id}`);
      } catch (error) {
        this.logger.error(
          'Failed to parse GCP_SERVICE_ACCOUNT_JSON. Falling back to Application Default Credentials.',
          error.stack,
        );
      }
    } else {
      this.logger.log('Initializing StorageService with Application Default Credentials (ADC).');
    }

    this.storage = new Storage(storageConfig);

    this.logger.log(`   Using Project ID: ${storageConfig.projectId}`);
    this.logger.log(`   Target Bucket: ${this.bucketName}`);
  }


  /**
   * Generates a v4 signed URL for uploading a file.
   * @param objectPath The full path for the object in the GCS bucket.
   * @param contentType The MIME type of the file being uploaded.
   * @returns A promise that resolves to the signed URL string.
   */
  async generateUploadUrl(
    objectPath: string,
    contentType: string,
  ): Promise<string> {
    this.logger.log('🔗 ===== GENERATING SIGNED URL =====');
    this.logger.log(`📁 Object path: ${objectPath}`);
    this.logger.log(`📄 Content type: ${contentType}`);

    const options = {
      version: 'v4' as const,
      action: 'write' as const,
      expires: Date.now() + 15 * 60 * 1000, // 15 minutes
      contentType: contentType, // Generic binary data for production compatibility
    };

    try {
      this.logger.log('⏳ Attempting to generate signed URL...');
      const [url] = await this.storage
        .bucket(this.bucketName)
        .file(objectPath)
        .getSignedUrl(options);

      this.logger.log('✅ Successfully generated signed URL.');
      return url;
    } catch (error) {
      this.logger.error('❌ ===== SIGNED URL GENERATION FAILED =====');
      this.logger.error('Error message:', error.message);
      // The error is likely related to permissions if the auth verification above passed.
      // Ensure the service account has 'Service Account Token Creator' role on itself.
      throw error;
    }
  }


  /**
   * Constructs the public URL for a given object path.
   * @param objectPath The full path for the object in the GCS bucket.
   * @returns The public URL string.
   */
  getPublicUrl(objectPath: string): string {
    return `https://storage.googleapis.com/${this.bucketName}/${objectPath}`;
  }

  /**
   * Extracts the object path from a public URL.
   * @param publicUrl The public URL of the file.
   * @returns The object path within the bucket.
   */
  getObjectPathFromUrl(publicUrl: string): string {
    const baseUrl = `https://storage.googleapis.com/${this.bucketName}/`;
    if (!publicUrl.startsWith(baseUrl)) {
      throw new Error('Invalid public URL format');
    }
    return publicUrl.replace(baseUrl, '');
  }

  /**
   * Deletes a file from Google Cloud Storage.
   * @param objectPath The full path for the object in the GCS bucket.
   * @returns A promise that resolves when the file is deleted.
   */
  async deleteFile(objectPath: string): Promise<void> {
    this.logger.log('🗑️ ===== DELETING FILE FROM STORAGE =====');
    this.logger.log(`📁 Object path: ${objectPath}`);

    try {
      this.logger.log('⏳ Attempting to delete file...');
      await this.storage
        .bucket(this.bucketName)
        .file(objectPath)
        .delete();

      this.logger.log('✅ Successfully deleted file from storage.');
    } catch (error) {
      this.logger.error('❌ ===== FILE DELETION FAILED =====');
      this.logger.error('Error message:', error.message);

      // If the file doesn't exist, we can consider it a success
      if (error.code === 404) {
        this.logger.warn('File not found, considering deletion successful.');
        return;
      }

      throw error;
    }
  }

  /**
   * Deletes a file using its public URL.
   * @param publicUrl The public URL of the file to delete.
   * @returns A promise that resolves when the file is deleted.
   */
  async deleteFileByUrl(publicUrl: string): Promise<void> {
    const objectPath = this.getObjectPathFromUrl(publicUrl);
    await this.deleteFile(objectPath);
  }
}