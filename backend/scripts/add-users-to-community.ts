import { PrismaClient, UserRole } from '@prisma/client';

const prisma = new PrismaClient();

// --- Configuration ---
const communityName = 'IBD Ambassadors DK';
const userEmails = [
  '<EMAIL>',
];
// --- End of Configuration ---

async function main() {
  console.log(
    `Starting script to add ${userEmails.length} users to community "${communityName}"`,
  );

  const community = await prisma.community.findUnique({
    where: { name: communityName },
  });

  if (!community) {
    console.error(`Error: Community with name "${communityName}" not found.`);
    process.exit(1);
  }

  console.log(
    `Found community: ${community.name} (ID: ${community.id})`,
  );

  const users = await prisma.users.findMany({
    where: {
      email: {
        in: userEmails,
      },
    },
  });

  const foundEmails = users.map((u) => u.email);
  const notFoundEmails = userEmails.filter((email) => !foundEmails.includes(email));

  if (notFoundEmails.length > 0) {
    console.warn(`Warning: Could not find users for the following emails: ${notFoundEmails.join(', ')}`);
  }
  
  if (users.length === 0) {
    console.log('No existing users found with the provided emails. Exiting.');
    return;
  }

  for (const user of users) {
    const pseudonym = await prisma.user_pseudonyms.findUnique({
      where: { userId: user.id },
    });

    if (!pseudonym) {
       console.warn(`Warning: User ${user.email} does not have a pseudonym (healthID). Skipping.`);
       continue;
     }

    const userHealthId = pseudonym.pseudonymId;

    const existingMembership = await prisma.communityMembership.findUnique({
      where: {
        user_healthID_communityId: {
          user_healthID: userHealthId,
          communityId: community.id,
        },
      },
    });

    if (existingMembership) {
      console.log(
        `User ${user.email} is already a member of community "${communityName}".`,
      );
    } else {
      await prisma.communityMembership.create({
        data: {
          user_healthID: userHealthId,
          communityId: community.id,
          memberRole: 'MEMBER',
          joinedAt: new Date(),
        },
      });
      console.log(
        `Successfully added user ${user.email} to community "${communityName}".`,
      );
    }
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 