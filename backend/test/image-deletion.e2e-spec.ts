import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { StorageService } from '../src/storage/storage.service';

describe('Image Deletion (e2e)', () => {
  let app: INestApplication;
  let storageService: StorageService;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    storageService = moduleFixture.get<StorageService>(StorageService);
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('StorageService', () => {
    it('should extract object path from public URL correctly', () => {
      const publicUrl = 'https://storage.googleapis.com/chronicare-uploads-prod/thread-images/user123/1234567890-abc123.jpeg';
      const objectPath = storageService.getObjectPathFromUrl(publicUrl);
      expect(objectPath).toBe('thread-images/user123/1234567890-abc123.jpeg');
    });

    it('should throw error for invalid public URL format', () => {
      const invalidUrl = 'https://example.com/invalid-url.jpg';
      expect(() => {
        storageService.getObjectPathFromUrl(invalidUrl);
      }).toThrow('Invalid public URL format');
    });
  });

  describe('GraphQL deleteThreadImage mutation', () => {
    it('should be available in the schema', async () => {
      const query = `
        query {
          __schema {
            mutationType {
              fields {
                name
                type {
                  name
                }
              }
            }
          }
        }
      `;

      const response = await request(app.getHttpServer())
        .post('/graphql')
        .send({ query })
        .expect(200);

      const mutations = response.body.data.__schema.mutationType.fields;
      const deleteThreadImageMutation = mutations.find(
        (field: any) => field.name === 'deleteThreadImage'
      );

      expect(deleteThreadImageMutation).toBeDefined();
      expect(deleteThreadImageMutation.type.name).toBe('Boolean');
    });
  });
});
